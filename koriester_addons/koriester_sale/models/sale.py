from odoo import _, api, fields, models
from odoo.exceptions import ValidationError
from odoo.tools.misc import split_every

READONLY_FIELD_STATES = {
    state: [('readonly', True)] for state in {'sale', 'done', 'cancel'}
}

ORDER_TYPES = [
    ('finish', 'Finish'),
    ('makloon', 'Makloon'),
    ('purchase', 'Purchase'),
]

ORDER_PREFIX = {
    'makloon': 'KR',
    'finish': 'KRNV',
    'purchase': 'KRP',
}


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    def _domain_product_id(self):
        return [
            ('sale_ok', '=', True),
            ('type', '=', 'product'),
            ('categ_type', '=', 'finished'),
        ]

    def _domain_tax_id(self):
        return [('type_tax_use', '=', 'sale')]

    note = fields.Text(states=READONLY_FIELD_STATES)
    order_type = fields.Selection(
        selection=ORDER_TYPES,
        required=True,
        states=READONLY_FIELD_STATES,
    )
    product_id = fields.Many2one(
        'product.product',
        change_default=True,
        check_company=True,
        domain=_domain_product_id,
        ondelete='restrict',
        required=True,
        states=READONLY_FIELD_STATES,
        string='Product',
    )
    uom_category_id = fields.Many2one(
        related='product_id.uom_id.category_id', readonly=True
    )
    uom_id = fields.Many2one(
        'uom.uom',
        string='Unit of Measure',
        required=True,
        states=READONLY_FIELD_STATES,
    )
    tax_id = fields.Many2one(
        'account.tax',
        check_company=True,
        domain=_domain_tax_id,
        states=READONLY_FIELD_STATES,
        string='Tax',
    )
    width = fields.Float(
        default=0.0,
        digits='Product Unit of Measure',
        states=READONLY_FIELD_STATES,
        string='Width',
    )
    purchase_order = fields.Char(string='Purchase Order', states=READONLY_FIELD_STATES)
    greige_code = fields.Char(string='Greige Code', states=READONLY_FIELD_STATES)
    shrinkage = fields.Float(string='Shrinkage (%)', states=READONLY_FIELD_STATES)
    loss = fields.Float(string='Loss (%)', states=READONLY_FIELD_STATES)
    band = fields.Char(string='Band', states=READONLY_FIELD_STATES)
    tag = fields.Char(string='Tag', states=READONLY_FIELD_STATES)
    stamping = fields.Char(string='Stamping', states=READONLY_FIELD_STATES)
    pcs_length = fields.Char(string='Pcs Length', states=READONLY_FIELD_STATES)
    sample = fields.Integer(string='Sample (set)', states=READONLY_FIELD_STATES)
    is_labeled = fields.Boolean(string='Is Labeled', copy=False)
    allow_revision = fields.Boolean(
        string='Allow Revision', compute='_compute_allow_revision'
    )
    total_qty_greige = fields.Float(
        string='Total Qty Greige', compute='_compute_total_qty_greige'
    )

    @api.onchange('product_id')
    def _onchange_product_id(self):
        self.uom_id = self.product_id.uom_id.id
        self.order_line = False

    @api.onchange('uom_id')
    def _onchage_uom_id(self):
        for line in self.order_line:
            line.product_uom = self.uom_id.id

    @api.onchange('tax_id')
    def _onchange_tax_id(self):
        self.order_line._compute_tax_id()

    @api.onchange('shrinkage', 'loss')
    def _onchange_shrinkage_loss(self):
        for line in self.order_line:
            line._onchange_qty_greige()

    @api.depends('picking_ids')
    def _compute_allow_revision(self):
        self.allow_revision = self.state == 'sale'

    @api.depends('order_line.qty_greige')
    def _compute_total_qty_greige(self):
        for order in self:
            order.total_qty_greige = sum(order.order_line.mapped('qty_greige'))

    @api.constrains('order_line')
    def _check_order_line(self):
        for order in self:
            has_duplicate_lot = len(order.order_line.mapped('lot_id')) != len(
                order.order_line
            )
            if has_duplicate_lot:
                raise ValidationError(
                    _('You cannot have more than one lot in the order lines.')
                )

    def _get_order_sequence(self, order_type, date_order):
        sequence = self.env.ref('koriester_sale.seq_job_order')
        return (
            f'{ORDER_PREFIX[order_type]}{sequence.next_by_id(sequence_date=date_order)}'
        )

    @api.model_create_multi
    def create(self, vals_list):
        result = super().create(vals_list)
        for order in result:
            order.name = self._get_order_sequence(order.order_type, order.date_order)
        return result

    def write(self, vals):
        order_type = vals.get('order_type', False)
        if order_type and order_type != self.order_type:
            name_prefix = ORDER_PREFIX[order_type] + '/'
            vals['name'] = name_prefix + '/'.join(self.name.split('/')[1:])
        return super().write(vals)

    def action_confirm(self):
        if self.env.user.has_group('sales_team.group_sale_salesman'):
            return super(SaleOrder, self.sudo()).action_confirm()
        return super().action_confirm()

    def action_cancel(self):
        if sum(self.order_line.mapped('qty_labeled')) > 0.0:
            raise ValidationError(
                _(
                    'You cannot cancel this Sale Order\
                    because it has already been labeled.'
                )
            )
        return super().action_cancel()

    def action_close_labeled(self):
        self.is_labeled = True

    def action_reopen_labeled(self):
        self.is_labeled = False

    # Start of functions for JO report
    def _get_total_greige_meter(self):
        uom_meter = self.env.ref('uom.product_uom_meter')
        return self.uom_id._compute_quantity(
            sum(self.order_line.mapped('qty_greige')), uom_meter
        )

    def _get_splitted_order_lines(self):
        result = []
        for order_chunk in split_every(30, self.order_line):
            result.append({idx: val for idx, val in enumerate(order_chunk)})
        return result

    # End of functions for JO report


class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    lot_id = fields.Many2one(required=True, copy=True)
    product_uom_qty = fields.Float(readonly=True)
    qty_greige = fields.Float(
        copy=True,
        default=0.0,
        digits='Product Unit of Measure',
        string='Quantity Greige',
    )
    is_revision = fields.Boolean(string='Is Revision', default=False, copy=False)

    def _compute_tax_id(self):
        for line in self:
            line = line.with_company(line.company_id)
            fpos = (
                line.order_id.fiscal_position_id
                or line.order_id.fiscal_position_id.get_fiscal_position(
                    line.order_partner_id.id
                )
            )
            line.tax_id = fpos.map_tax(
                line.order_id.tax_id, line.product_id, line.order_id.partner_shipping_id
            )

    @api.onchange('lot_id')
    def _onchange_lot_id(self):
        self.name = self.get_sale_order_line_multiline_description_sale(self.product_id)

    @api.onchange('product_id')
    def _onchange_product_id_set_lot_domain(self):
        # Change deprecated return from OCA module
        return {}

    @api.onchange('qty_greige')
    def _onchange_qty_greige(self):
        self.product_uom_qty = self.qty_greige / (
            1 + (self.order_id.shrinkage + self.order_id.loss) / 100
        )

    def name_get(self):
        return [(line.id, line.name) for line in self]

    def get_sale_order_line_multiline_description_sale(self, product):
        if self.lot_id:
            return self.lot_id.name
        return super().get_sale_order_line_multiline_description_sale(product)

    @api.constrains('qty_greige', 'product_uom_qty')
    def _check_quantity(self):
        if self.qty_greige <= 0.0 or self.product_uom_qty <= 0.0:
            raise ValidationError(_('Quantity greige must be greater than zero!'))
