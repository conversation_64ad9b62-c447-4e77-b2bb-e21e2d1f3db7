<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    
    <!-- Start Operations Menu -->
    <menuitem
        id="menu_slot_mutation"
        name="Slot Mutation"
        parent="stock.menu_stock_warehouse_mgmt"
        sequence="7"
        action="action_slot_mutation"
    />
    <menuitem
        id="menu_stock_return"
        name="Stock Returns"
        parent="stock.menu_stock_warehouse_mgmt"
        sequence="8"
        action="action_stock_return"
    />
    <menuitem
        id="menu_asset_maintenance"
        name="Asset Maintenance"
        parent="stock.menu_stock_warehouse_mgmt"
        sequence="150"
        action="action_asset_maintenance"
    />
    <!-- End Operations Menu -->

    <menuitem
        id="menu_packing"
        name="Packing"
        parent="stock.menu_stock_root"
        sequence="3"
    >
        <menuitem
            id="menu_stock_label"
            name="Labels"
            sequence="0"
            action="action_stock_label"
        />
        <menuitem
            id="menu_create_label"
            name="Create Label"
            sequence="10"
            action="action_create_label"
        />
        <menuitem
            id="menu_stock_label_report"
            name="Report Label"
            sequence="15"
            action="action_stock_label_report"
        />
    </menuitem>

    <menuitem
        id="menu_stock_slot"
        name="Slots"
        parent="stock.menu_warehouse_config"
        sequence="0"
        action="action_stock_slot"
    />

    <menuitem
        id="menu_stock_grade"
        name="Grades"
        parent="stock.menu_product_in_config_stock"
        sequence="70"
        action="action_stock_grade"
    />

    <menuitem
        id="menu_stock_asset"
        name="Assets"
        parent="stock.menu_product_in_config_stock"
        sequence="80"
        action="action_stock_asset"
    />

    <menuitem
        id="menu_stock_mutation"
        name="Stock Mutation"
        parent="stock.menu_warehouse_report"
        sequence="200"
        action="action_stock_mutation"
    />

    <menuitem
        id="menu_stock_usage_report"
        name="Usage Report"
        parent="stock.menu_warehouse_report"
        sequence="210"
        action="action_stock_usage_report"
    />
</odoo>
