import base64
import json

from odoo import _, api, fields, models
from odoo.exceptions import UserError, ValidationError
from odoo.tools import float_compare


class Inventory(models.Model):
    _inherit = 'stock.inventory'

    has_lines = fields.Boolean(string='Has Lines', compute='_compute_has_lines')
    by_label = fields.Boolean(
        string='By Label',
        help='Check this box to create inventory lines based on labels.',
        default=True,
    )
    label_count = fields.Integer(string='Label Count', compute='_compute_label_count')
    product_ids_domain = fields.Char(
        compute='_compute_product_ids_domain', readonly=True
    )

    @api.depends('line_ids')
    def _compute_has_lines(self):
        for inventory in self:
            inventory.has_lines = bool(
                self.env['stock.inventory.line']
                ._search([('inventory_id', '=', inventory.id)], limit=1)
                ._result
            )

    @api.depends('location_ids')
    def _compute_product_ids_domain(self):
        for inventory in self:
            domain = []
            if inventory.location_ids:
                domain = [
                    (
                        'id',
                        'in',
                        inventory.location_ids.mapped('quant_ids.product_id').ids,
                    ),
                ]
            inventory.product_ids_domain = json.dumps(domain)

    @api.depends('line_ids.label_count')
    def _compute_label_count(self):
        for inventory in self:
            inventory.label_count = self.env['stock.label'].search_count(
                [
                    ('state', '=', 'assigned'),
                    ('inventory_line_id.inventory_id', '=', inventory.id),
                ]
            )

    def _get_inventory_lines_values(self):
        """Override to include label_ids in inventory lines."""
        lines_values = super()._get_inventory_lines_values()
        if self.by_label:
            for val in lines_values:
                labels = self.env['stock.label']._search(
                    [
                        ('state', '=', 'assigned'),
                        ('color_way_id', '=', val['prod_lot_id']),
                    ]
                )
                val['label_ids'] = [
                    (0, 0, {'label_id': label_id}) for label_id in labels._result
                ]
        return lines_values

    def action_validate(self):
        if not self.exists():
            return
        self.ensure_one()
        if self.state != 'confirm':
            raise UserError(
                _(
                    "You can't validate the inventory '%s', maybe this inventory "
                    "has been already validated or isn't ready.",
                    self.name,
                )
            )
        inventory_lines = self.line_ids.filtered(
            lambda l: l.product_id.tracking in ['lot', 'serial']
            and not l.prod_lot_id
            and l.theoretical_qty != l.product_qty
        )
        lines = self.line_ids.filtered(
            lambda l: float_compare(
                l.product_qty, 1, precision_rounding=l.product_uom_id.rounding
            )
            > 0
            and l.product_id.tracking == 'serial'
            and l.prod_lot_id
        )
        if inventory_lines and not lines:
            wiz_lines = [
                (0, 0, {'product_id': product.id, 'tracking': product.tracking})
                for product in inventory_lines.mapped('product_id')
            ]
            wiz = self.env['stock.track.confirmation'].create(
                {'inventory_id': self.id, 'tracking_line_ids': wiz_lines}
            )
            return {
                'name': _('Tracked Products in Inventory Adjustment'),
                'type': 'ir.actions.act_window',
                'view_mode': 'form',
                'views': [(False, 'form')],
                'res_model': 'stock.track.confirmation',
                'target': 'new',
                'res_id': wiz.id,
            }
        self._action_done()
        self.line_ids._check_company()
        self._check_company()
        return True

    def _action_done(self):
        result = super()._action_done()
        if self.by_label:
            labels = self.line_ids.mapped('label_ids')
            if not labels:
                raise ValidationError(
                    _('No labels found for this inventory adjustment.')
                )
            # Update the state of labels that have been assigned but not counted
            to_done_labels = labels.filtered(
                lambda l: l.state == 'assigned' and l.product_qty == 0
            )
            for line in to_done_labels:
                line.label_id.write(
                    {'state': 'done', 'inventory_line_id': line.inventory_line_id.id}
                )

            # Update the state of labels that have been not assigned but counted
            to_assign_labels = labels.filtered(
                lambda l: l.state != 'assigned' and l.product_qty > 0
            )
            for line in to_assign_labels:
                line.label_id.write(
                    {
                        'state': 'assigned',
                        'inventory_line_id': line.inventory_line_id.id,
                    }
                )
        return result

    def action_view_lines(self):
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'view_mode': 'tree',
            'name': _('Inventory Lines'),
            'res_model': 'stock.inventory.line',
            'view_id': self.env.ref(
                'koriester_stock.view_stock_inventory_line_tree_readonly'
            ).id,
            'context': {'default_is_editable': False, 'readonly_location_id': True},
            'domain': [
                ('inventory_id', '=', self.id),
                ('location_id.usage', 'in', ['internal', 'transit']),
            ],
        }

    def action_view_labels(self):
        action = self.env['ir.actions.actions']._for_xml_id(
            'koriester_stock.action_stock_label'
        )
        action['domain'] = [
            ('state', '=', 'assigned'),
            ('inventory_line_id', 'in', self.line_ids.ids),
        ]
        action['context'] = {}
        return action


class StockInventoryLine(models.Model):
    _inherit = 'stock.inventory.line'

    name = fields.Char(string='Name', compute='_compute_name', store=True)
    by_label = fields.Boolean(related='inventory_id.by_label', store=True)
    label_qty = fields.Float(
        compute='_compute_qty', store=True, digits='Product Unit of Measure'
    )
    product_qty = fields.Float(compute='_compute_qty', store=True)
    file_name = fields.Char(string='File Name', copy=False)
    file_barcodes = fields.Binary(
        string='Barcodes',
        attachment=True,
        copy=False,
        states={'done': [('readonly', True)]},
    )
    label_ids = fields.One2many(
        'stock.inventory.label',
        'inventory_line_id',
        string='Labels',
        states={'done': [('readonly', True)]},
    )
    label_count = fields.Integer(string='Label Count', compute='_compute_label_count')

    @api.depends('product_id', 'prod_lot_id')
    def _compute_name(self):
        for line in self:
            line.name = line.product_id.name
            if line.prod_lot_id:
                line.name += f' ({line.prod_lot_id.name})'

    @api.depends('by_label', 'label_ids.label_qty', 'label_ids.product_qty')
    def _compute_qty(self):
        for line in self:
            # If the inventory is by label, sum the counted quantities from labels
            if line.by_label and line.label_ids:
                line.label_qty = sum(line.label_ids.mapped('label_qty'))
                line.product_qty = sum(line.label_ids.mapped('product_qty'))

    @api.depends('label_ids')
    def _compute_label_count(self):
        for line in self:
            line.label_count = len(
                line.label_ids.filtered(
                    lambda l: l.label_id.inventory_line_id.id == line.id
                    and l.state == 'assigned'
                )
            )

    def unlink(self):
        for line in self:
            if line.state == 'done':
                raise ValidationError(
                    _('You cannot delete an inventory line that has been validated.')
                )
        return super().unlink()

    def _compile_file_barcodes(self, file):
        try:
            result = base64.b64decode(file).decode().replace('\r', '').split('\n')
        except Exception:
            raise ValidationError(
                _(
                    'Something wrong with the files!. '
                    'Please correct your files before proceeding.'
                )
            )
        return result

    def _get_labels_domain(self):
        self.ensure_one()
        barcodes = self._compile_file_barcodes(self.file_barcodes)
        domain = [('name', 'in', barcodes), ('color_way_id', '=', self.prod_lot_id.id)]
        return domain

    @api.onchange('file_barcodes')
    def _onchange_file_barcodes(self):
        if self.file_barcodes:
            domain = self._get_labels_domain()
            imported_labels = self.env['stock.label'].search(domain)

            if not imported_labels:
                self.file_barcodes = False
                raise ValidationError(
                    _(
                        'Barcode label not found!. '
                        'Please fix your files before proceeding.'
                    )
                )

            for label in imported_labels:
                field_uom = self.env['create.label']._get_fields_uom(label.uom_id.id)
                existing_label = self.label_ids.filtered(lambda l: l.label_id == label)
                if existing_label:
                    existing_label.product_qty = existing_label.label_qty
                else:
                    product_qty = 0.0
                    if field_uom == 'qty_yard':
                        product_qty = label.qty_yard
                    elif field_uom == 'qty_meter':
                        product_qty = label.qty_meter
                    elif field_uom == 'qty_kg':
                        product_qty = label.qty_kg

                    self.label_ids = [
                        (0, 0, {'label_id': label.id, 'product_qty': product_qty})
                    ]
        else:
            self.label_ids -= self.label_ids.filtered(lambda l: l.state != 'assigned')
            self.label_ids.write({'product_qty': 0.0})

    def action_view_details(self):
        self.ensure_one()
        view_id = self.env.ref('koriester_stock.view_stock_inventory_line_form').id
        if self.state == 'done':
            view_id = self.env.ref(
                'koriester_stock.view_stock_inventory_line_form_readonly'
            ).id

        return {
            'type': 'ir.actions.act_window',
            'name': 'Inventory Details',
            'res_model': 'stock.inventory.line',
            'view_mode': 'form',
            'res_id': self.id,
            'target': 'self',
            'view_id': view_id,
        }

    def action_view_labels(self):
        action = self.env['ir.actions.actions']._for_xml_id(
            'koriester_stock.action_stock_label'
        )
        action['domain'] = [
            ('state', '=', 'assigned'),
            ('inventory_line_id', '=', self.id),
        ]
        action['context'] = {'default_inventory_line_id': self.id}
        return action


class StockInventoryLabel(models.Model):
    _name = 'stock.inventory.label'
    _description = 'Stock Inventory Label'

    inventory_line_id = fields.Many2one(
        'stock.inventory.line',
        string='Inventory Line',
        required=True,
        ondelete='cascade',
    )
    color_way_id = fields.Many2one(
        related='inventory_line_id.prod_lot_id', string='Color Way'
    )
    label_id = fields.Many2one('stock.label', string='Label', required=True)
    label_qty = fields.Float(
        string='Qty', digits='Product Unit of Measure', compute='_compute_label_qty'
    )
    product_qty = fields.Float(string='Counted', digits='Product Unit of Measure')
    state = fields.Selection(related='label_id.state')

    @api.depends(
        'label_id.uom_id', 'label_id.qty_yard', 'label_id.qty_meter', 'label_id.qty_kg'
    )
    def _compute_label_qty(self):
        for line in self:
            if not line.label_id or line.label_id.state != 'assigned':
                line.label_qty = 0.0
                continue
            field_uom = self.env['create.label']._get_fields_uom(
                line.label_id.uom_id.id
            )
            if field_uom == 'qty_yard':
                line.label_qty = line.label_id.qty_yard
            elif field_uom == 'qty_meter':
                line.label_qty = line.label_id.qty_meter
            elif field_uom == 'qty_kg':
                line.label_qty = line.label_id.qty_kg
            else:
                line.label_qty = 0.0
