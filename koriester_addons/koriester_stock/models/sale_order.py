from odoo import api, fields, models


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    total_qty = fields.Float(string='Total Qty', compute='_compute_total_qty')
    total_qty_delivered = fields.Float(
        string='Total Qty Delivered', compute='_compute_total_qty'
    )
    total_qty_outstanding = fields.Float(
        string='Total Qty Outstanding', compute='_compute_total_qty'
    )
    label_completed = fields.Boolean(
        string='Label Completed', compute='_compute_label_completed', store=True
    )

    @api.depends('order_line')
    def _compute_total_qty(self):
        for order in self:
            order.total_qty = sum(order.order_line.mapped('product_uom_qty'))
            order.total_qty_delivered = sum(order.order_line.mapped('qty_delivered'))
            order.total_qty_outstanding = order.total_qty - order.total_qty_delivered

    @api.depends('order_line.qty_available')
    def _compute_label_completed(self):
        for order in self:
            order.label_completed = (
                False
                if order.order_line.filtered(lambda l: l.qty_available > 0)
                else True
            )

    def _action_confirm(self):
        # TODO: add validation qty must be greater than zero
        result = super()._action_confirm()
        for order in self:
            if order.picking_ids:
                order.picking_ids[0].product_id = order.product_id.id

        return result


class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    label_ids = fields.One2many('stock.label', 'order_line_id', string='Labels')
    qty_labeled = fields.Float(
        compute='_compute_quantity',
        digits='Product Unit of Measure',
        readonly=True,
        store=True,
        string='Quantity Labeled',
    )
    qty_available = fields.Float(
        compute='_compute_quantity',
        digits='Product Unit of Measure',
        readonly=True,
        store=True,
        string='Quantity Available',
    )

    @api.depends('product_uom_qty', 'label_ids')
    def _compute_quantity(self):
        get_fields_uom = self.env['create.label']._get_fields_uom
        for line in self:
            labels = line.label_ids.filtered(lambda l: l.state != 'correction')
            line.qty_labeled = sum(
                labels.mapped(
                    lambda l: get_fields_uom(l.uom_id.id, False)
                    and getattr(l, get_fields_uom(l.uom_id.id, False))
                    or 0
                )
            )
            line.qty_available = line.product_uom_qty - line.qty_labeled

    def _prepare_procurement_values(self, group_id=False):
        values = super()._prepare_procurement_values(group_id)
        values['lot_id'] = self.lot_id.id
        return values
