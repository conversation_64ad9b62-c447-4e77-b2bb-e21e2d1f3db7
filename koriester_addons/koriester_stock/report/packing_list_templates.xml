<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <template id="report_packing_list_document">
        <t t-call="web.basic_layout">
            <style>
                .page {
                    color: black;
                }
                .table-bordered td {
                    text-align: center;
                    vertical-align: middle;
                }
                .table-bordered th {
                    text-align: center;
                    vertical-align: middle;
                    font-weight: bolder;
                    border: 1px solid black;
                }
                .table-bordered > thead > tr > th{
                    border: 1px solid black;
                }
                .table-bordered > tbody > tr > td{
                    border: 1px solid black;
                }
            </style>
            <t t-foreach="docs" t-as="o">
                <div class="page">
                    <div class="row">
                        <div class="col-12 text-center">
                            <h4>
                                <b>PACKING LIST</b>
                            </h4>
                            <h5>
                                <b t-field="o.name" />
                            </h5>
                        </div>
                    </div>
                    <div class="row oe_mt8">
                        <table
                            class="table table-sm table-borderless"
                            style="margin-bottom: 3px;"
                        >
                            <tr>
                                <td width="6%">DATE</td>
                                <td width="2%">:</td>
                                <td width="60%" class="text-left">
                                    <span
                                        t-esc="datetime.datetime.now()"
                                        t-options="{'widget': 'datetime', 'format': 'dd MMM yyyy'}"
                                    />
                                </td>
                                <td width="5%">SJ</td>
                                <td width="2%">:</td>
                                <td class="text-left" rowspan="3">
                                    <span t-field="o.delivery_note" />
                                </td>
                            </tr>
                            <tr>
                                <td>JO</td>
                                <td>:</td>
                                <td class="text-left" colspan="3">
                                    <span t-field="o.sale_id" />
                                </td>
                            </tr>
                            <tr>
                                <td>ITEM</td>
                                <td>:</td>
                                <td class="text-left" colspan="3">
                                    <span t-field="o.product_id.name" />
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="row">
                        <table class="table table-sm table-bordered">
                            <thead>
                                <tr>
                                    <th>COLOR</th>
                                    <th>GRADE</th>
                                    <th>1</th>
                                    <th>2</th>
                                    <th>3</th>
                                    <th>4</th>
                                    <th>5</th>
                                    <th>6</th>
                                    <th>7</th>
                                    <th>8</th>
                                    <th>9</th>
                                    <th>10</th>
                                    <th>PCS</th>
                                    <th>YARD</th>
                                </tr>
                            </thead>
                            <t t-set="total_pcs" t-value="0" />
                            <t t-set="total_yard" t-value="0.0" />
                            <tbody>
                                <t
                                    t-set="grouped_labels"
                                    t-value="o._get_grouped_labels()"
                                />
                                <tr t-foreach="grouped_labels.keys()" t-as="lot">
                                    <t
                                        t-value="o._get_splitted_label_lines(grouped_labels[lot])"
                                        t-set="splited_label"
                                    />
                                    <td t-att-rowspan="len(splited_label)+1">
                                        <span t-esc="lot[0].name" />
                                        <t t-if="lot[1] and lot[1] != '-'">
                                            LOT
                                            <span t-esc="lot[1]" />
                                        </t>
                                        <t t-set="current_grade" t-value="False" />

                                        <tr t-foreach="splited_label" t-as="label">
                                            <t
                                                t-set="grade"
                                                t-value="list(label.keys())[0]"
                                            />
                                            <t
                                                t-set="rowspan"
                                                t-value="list(label.values())[0][0]"
                                            />
                                            <t
                                                t-set="values"
                                                t-value="list(label.values())[0][1]"
                                            />
                                            <t
                                                t-set="qty_pcs"
                                                t-value="list(label.values())[0][2]"
                                                t-options="{'widget': 'float', 'decimal_precision': 'Product Unit of Measure'}"
                                            />
                                            <t
                                                t-set="qty_yard"
                                                t-value="list(label.values())[0][3]"
                                                t-options="{'widget': 'float', 'decimal_precision': 'Product Unit of Measure'}"
                                            />

                                            <t t-if="current_grade != grade">
                                                <td t-att-rowspan="rowspan">
                                                    <span t-esc="grade.name" />
                                                </td>
                                            </t>

                                            <t t-foreach="range(10)" t-as="i">
                                                <td>
                                                    <t t-if="i &lt; len(values)">
                                                        <span
                                                            t-esc="values[i].qty_yard"
                                                            t-options="{'widget': 'float', 'decimal_precision': 'Product Unit of Measure'}"
                                                        />
                                                    </t>
                                                </td>
                                            </t>

                                            <t t-if="current_grade != grade">
                                                <td t-att-rowspan="rowspan">
                                                    <span
                                                        t-esc="qty_pcs"
                                                        t-options="{'widget': 'float', 'decimal_precision': 'Product Unit of Measure'}"
                                                    />
                                                </td>
                                                <td t-att-rowspan="rowspan">
                                                    <span
                                                        t-esc="qty_yard"
                                                        t-options="{'widget': 'float', 'decimal_precision': 'Product Unit of Measure'}"
                                                    />
                                                </td>
                                                <t
                                                    t-set="total_pcs"
                                                    t-value="total_pcs + qty_pcs"
                                                />
                                                <t
                                                    t-set="total_yard"
                                                    t-value="total_yard + qty_yard"
                                                />
                                            </t>
                                            <t t-set="current_grade" t-value="grade" />
                                        </tr>
                                    </td>
                                </tr>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="12">TOTAL</th>
                                    <th>
                                        <span
                                            t-esc="total_pcs"
                                            t-options="{'widget': 'float', 'decimal_precision': 'Product Unit of Measure'}"
                                        />
                                    </th>
                                    <th>
                                        <span
                                            t-esc="total_yard"
                                            t-options="{'widget': 'float', 'decimal_precision': 'Product Unit of Measure'}"
                                        />
                                    </th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </t>
        </t>
    </template>
</odoo>
