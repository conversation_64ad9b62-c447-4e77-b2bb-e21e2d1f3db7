<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <template id="report_deliveryslip_document">
        <t t-call="web.basic_layout">
            <style>
                .page {
                    color: black;
                    font-size: 10pt !important;
                }
                .header {
                    border-bottom: 3px black;
                }
                h2 {
                    font-weight: bolder;
                }
                .table-deliveryslip td {
                    text-align: center;
                    vertical-align: middle !important;
                }
                .table-deliveryslip th {
                    text-align: center;
                    vertical-align: middle !important;
                    font-weight: bolder;
                    border: 1px solid black;
                    padding-left: 10px;
                    padding-right: 10px;
                }
                .table-deliveryslip td {
                    border: 1px solid black;
                    padding-left: 10px;
                    padding-right: 10px;
                }
                .table-deliveryslip > tfoot td {
                    font-weight: bold;
                }
                .table-signature th {
                    text-align: center;
                }
                .table-signature td {
                    padding-top: 75px;
                }
                .empty-row td {
                    height: 20px;
                }
                .table-header-info td {
                    padding: 2px 4px;
                }
            </style>
            <t t-set="o" t-value="docs[0]" />
            <t t-set="o" t-value="o.with_context(lang=o._get_report_lang())" />

            <!-- Prepare data for pagination -->
            <t t-set="grouped_labels" t-value="docs._get_grouped_labels(False)" />
            <t t-set="all_data" t-value="[]" />
            <t t-set="total_pcs" t-value="0" />
            <t t-set="total_yard" t-value="0" />

            <!-- Convert grouped data to list for pagination -->
            <t t-foreach="grouped_labels.keys()" t-as="key">
                <t t-set="job_order" t-value="key[0]" />
                <t t-set="product" t-value="key[1]" />
                <t t-set="grade" t-value="key[2]" />
                <t t-set="labels" t-value="grouped_labels[key]" />
                <t t-set="qty_pcs" t-value="len(labels)" />
                <t t-set="qty_yard" t-value="sum(labels.mapped('qty_yard'))" />
                <t t-set="total_pcs" t-value="total_pcs + qty_pcs" />
                <t t-set="total_yard" t-value="total_yard + qty_yard" />
                <t
                    t-set="dummy"
                    t-value="all_data.append({
                    'job_order': job_order,
                    'product': product,
                    'grade': grade,
                    'qty_pcs': qty_pcs,
                    'qty_yard': qty_yard
                })"
                />
            </t>

            <!-- Calculate pagination -->
            <t t-set="rows_per_page" t-value="7" />
            <t t-set="total_rows" t-value="len(all_data)" />
            <t
                t-set="total_pages"
                t-value="(total_rows + rows_per_page - 1) // rows_per_page if total_rows > 0 else 1"
            />

            <!-- Generate pages -->
            <t t-foreach="range(total_pages)" t-as="page_index">
                <t t-set="start_index" t-value="page_index * rows_per_page" />
                <t
                    t-set="end_index"
                    t-value="min(start_index + rows_per_page, total_rows)"
                />
                <t t-set="page_data" t-value="all_data[start_index:end_index]" />

                <div class="page">
                    <t t-if="page_index > 0">
                        <div style="page-break-before: always;" />
                    </t>

                    <!-- Page Header -->
                    <div class="row">
                        <div class="col-12 text-center">
                            <h2>GATE PASS</h2>
                        </div>
                    </div>
                    <div class="row oe_mt8">
                        <div
                            name="company_address"
                            t-if="with_address"
                            class="col-6 text-left"
                        >
                            <div>
                                <strong>
                                    <span
                                        t-field="o.company_id.name"
                                        style="font-size: 12pt;"
                                    />
                                </strong>
                            </div>
                            <div>
                                <span>
                                    <strong>Factor :</strong>
                                </span>
                            </div>
                            <div class="company_address">
                                <div t-if="o.company_id.street">
                                    <span t-field="o.company_id.street" />
                                </div>
                                <div t-if="o.company_id.street2">
                                    <span t-field="o.company_id.street2" />
                                </div>
                                <div>
                                    <span
                                        t-if="o.company_id.city"
                                        t-field="o.company_id.city"
                                    />
                                    <span
                                        t-if="o.company_id.state_id and o.company_id.city"
                                    >, </span>
                                    <span
                                        t-if="o.company_id.state_id"
                                        t-field="o.company_id.state_id.name"
                                    />
                                    <span
                                        t-if="o.company_id.country_id and (o.company_id.city or o.company_id.state_id)"
                                    >, </span>
                                    <span
                                        t-if="o.company_id.country_id"
                                        t-field="o.company_id.country_id.name"
                                    />
                                    <span t-if="o.company_id.zip">
                                        <span t-field="o.company_id.zip" />
                                    </span>
                                </div>
                                <div t-if="o.company_id.phone">
                                    <span t-field="o.company_id.phone" />
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <table
                                class="table table-sm table-borderless table-header-info"
                            >
                                <tr>
                                    <td width="8%">DATE</td>
                                    <td width="2%">:</td>
                                    <td width="70%" class="text-left">
                                        <span
                                            t-esc="datetime.datetime.now()"
                                            t-options="{'widget': 'datetime', 'format': 'dd MMM yyyy'}"
                                        />
                                    </td>
                                </tr>
                                <tr>
                                    <td>COMPANY</td>
                                    <td>:</td>
                                    <td class="text-left">
                                        <span t-field="o.customer_code" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>GP NO</td>
                                    <td>:</td>
                                    <td class="text-left">
                                        <span t-field="o.delivery_note" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Data Table -->
                    <table
                        class="table table-sm table-bordered table-deliveryslip oe_mt8"
                    >
                        <thead>
                            <tr>
                                <th rowspan="2" width="5%">NO</th>
                                <th rowspan="2" width="40%">COMMODITY</th>
                                <th rowspan="2" width="10%">GRADE</th>
                                <th colspan="2">QUANTITY</th>
                                <th rowspan="2" width="25">REMARK</th>
                            </tr>
                            <tr>
                                <th width="10%">PIECE</th>
                                <th width="10%">YARD</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data rows for current page -->
                            <t t-foreach="page_data" t-as="row_data">
                                <t t-set="line_num" t-value="row_data_index + 1" />
                                <tr>
                                    <td class="text-right">
                                        <t t-esc="line_num" />
                                    </td>
                                    <td class="text-left">
                                        <t t-esc="row_data['product'].display_name" />
                                        -
                                        <t t-esc="row_data['job_order'].name" />
                                    </td>
                                    <td>
                                        <t t-esc="row_data['grade'].name" />
                                    </td>
                                    <td class="text-right">
                                        <t t-esc="row_data['qty_pcs']" />
                                    </td>
                                    <td class="text-right">
                                        <t
                                            t-esc="row_data['qty_yard']"
                                            t-options="{'widget': 'float', 'decimal_precision': 'Product Unit of Measure'}"
                                        />
                                    </td>
                                    <td />
                                </tr>
                            </t>

                            <!-- Fill empty rows to maintain 10 rows per page -->
                            <t
                                t-set="empty_rows_count"
                                t-value="rows_per_page - len(page_data)"
                            />
                            <t t-foreach="range(empty_rows_count)" t-as="empty_row">
                                <tr class="empty-row">
                                    <td />
                                    <td />
                                    <td />
                                    <td />
                                    <td />
                                    <td />
                                </tr>
                            </t>
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="3">GRAND TOTAL</td>
                                <td class="text-right">
                                    <t
                                        t-esc="sum([row['qty_pcs'] for row in page_data])"
                                    />
                                </td>
                                <td class="text-right">
                                    <t
                                        t-esc="sum([row['qty_yard'] for row in page_data])"
                                        t-options="{'widget': 'float', 'decimal_precision': 'Product Unit of Measure'}"
                                    />
                                </td>
                                <td />
                            </tr>
                        </tfoot>
                    </table>

                    <!-- Page Footer -->
                    <div class="row mt-4">
                        <div class="col-6">
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td width="20%">WITH CAR</td>
                                    <td style="border-bottom: 1px solid black;" />
                                    <td width="2%" />
                                    <td width="30%" class="p0">POLICE NUMBER</td>
                                    <td style="border-bottom: 1px solid black;" />
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-12">
                            <table
                                class="table table-sm table-borderless table-signature"
                            >
                                <tr>
                                    <th width="10%" />
                                    <th width="20%">RECEIVE BY</th>
                                    <th width="10%" />
                                    <th width="20%">APPROVE BY</th>
                                    <th width="10%" />
                                    <th width="20%">PREPARE BY</th>
                                    <th width="10%" />
                                </tr>
                                <tr>
                                    <td />
                                    <td style="border-bottom: 1px solid black;" />
                                    <td />
                                    <td style="border-bottom: 1px solid black;" />
                                    <td />
                                    <td style="border-bottom: 1px solid black;" />
                                    <td />
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </t>
        </t>
    </template>

    <template id="koriester_stock.report_deliveryslip">
        <t t-call="koriester_stock.report_deliveryslip_document" />
    </template>

    <template id="koriester_stock.report_deliveryslip_tax">
        <t t-call="koriester_stock.report_deliveryslip_document">
            <t t-set="with_address" t-value="True" />
        </t>
    </template>
</odoo>
