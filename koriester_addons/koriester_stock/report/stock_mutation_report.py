from collections import defaultdict

from xlsxwriter import utility

from odoo import _, models


class StockMutationReportXlsx(models.AbstractModel):
    _name = 'report.koriester_stock.stock_mutation_report_xlsx'
    _inherit = 'report.report_xlsx.abstract'

    def _get_data(self, mutation_wiz):
        label_domain = [
            ('state', 'in', ['draft', 'assigned', 'done']),
        ]
        if mutation_wiz.date_label:
            label_domain.append(('date_label', '<=', mutation_wiz.date_label))
        if mutation_wiz.product_ids:
            label_domain.append(('product_id', 'in', mutation_wiz.product_ids.ids))
        if mutation_wiz.lot_ids:
            label_domain.append(('color_way_id', 'in', mutation_wiz.lot_ids.ids))

        labels = mutation_wiz.env['stock.label'].search(label_domain)
        return labels

    def generate_xlsx_report(self, workbook, data, mutation_wiz):
        report_name = _('Stock Mutation.xlsx')
        worksheet = workbook.add_worksheet(report_name)

        header_format = workbook.add_format(
            {'bold': True, 'align': 'center', 'valign': 'vcenter'}
        )
        header_format.set_border()
        header_format.set_text_wrap()

        grades = self.env['stock.grade'].search([])
        grades_name = grades.mapped('name')
        headers = [
            'Job Order',
            'Customer',
            'Product',
            'Color Way',
            {'Packing': 'Total Packing'},
            {'Masuk': 'Total Masuk'},
            {'Keluar': 'Total Keluar'},
            {'Saldo Akhir': 'Total Akhir'},
        ]

        col_num = 0
        for header in headers:
            col = utility.xl_col_to_name(col_num)
            if isinstance(header, str):
                worksheet.merge_range(f'{col}1:{col}3', header, header_format)
            if isinstance(header, dict):
                g_num = 0
                for grade in grades_name:
                    grade_col = utility.xl_col_to_name(col_num + g_num)
                    grade_col_next = utility.xl_col_to_name((col_num + g_num) + 1)
                    worksheet.merge_range(
                        f'{grade_col}2:{grade_col_next}2', grade, header_format
                    )
                    worksheet.write(f'{grade_col}3', 'PCS', header_format)
                    worksheet.write(f'{grade_col_next}3', 'YARD', header_format)
                    g_num += 2

                col_num += len(grades) * 2
                col_next = utility.xl_col_to_name(col_num - 1)
                total_col = utility.xl_col_to_name(col_num)
                for key, val in header.items():
                    worksheet.merge_range(f'{col}1:{col_next}1', key, header_format)
                    worksheet.merge_range(
                        f'{total_col}1:{total_col}3', val, header_format
                    )

            col_num += 1

        labels = self._get_data(mutation_wiz)
        data = defaultdict(lambda: self.env['stock.label'])
        for label in labels:
            data[(label.job_order_id, label.color_way_id)] += label

        self._generate_worksheet(workbook, worksheet, data)
        worksheet.autofit()

    def _generate_worksheet(self, workbook, worksheet, data):
        body_format = workbook.add_format({'align': 'left', 'valign': 'vcenter'})
        body_format.set_border()
        number_format = workbook.add_format({'align': 'right', 'valign': 'vcenter'})
        number_format.set_border()
        draft_number_format = workbook.add_format(
            {'align': 'right', 'valign': 'vcenter', 'bg_color': '#EEEEEE'}
        )
        draft_number_format.set_border()

        grades = self.env['stock.grade'].search([])

        row = 3
        for key, labels in data.items():
            labels_out = labels.filtered(lambda l: l.state == 'done')
            labels_draft = labels.filtered(lambda l: l.state == 'draft')
            labels_in = labels - labels_draft

            if sum(labels_out.mapped('qty_yard')) >= sum(labels.mapped('qty_yard')):
                continue

            order = key[0]
            lot = key[1]
            worksheet.write(row, 0, order.name, body_format)
            worksheet.write(row, 1, order.partner_id.name, body_format)
            worksheet.write(row, 2, order.product_id.name, body_format)
            worksheet.write(row, 3, lot.name, body_format)

            # Total Draft
            qty_col = 4
            for grade in grades:
                label_draft = labels_draft.filtered(lambda l: l.grade_id == grade)
                if label_draft:
                    worksheet.write_number(
                        row, qty_col, len(label_draft), draft_number_format
                    )
                    worksheet.write_number(
                        row,
                        qty_col + 1,
                        sum(label_draft.mapped('qty_yard')),
                        draft_number_format,
                    )
                else:
                    worksheet.write(row, qty_col, '', draft_number_format)
                    worksheet.write(row, qty_col + 1, '', draft_number_format)

                qty_col += 2

            worksheet.write_number(
                row, qty_col, sum(labels_draft.mapped('qty_yard')), draft_number_format
            )

            # Labels In
            qty_col += 1
            for grade in grades:
                label = labels_in.filtered(lambda l: l.grade_id == grade)
                if label:
                    worksheet.write_number(row, qty_col, len(label), number_format)
                    worksheet.write_number(
                        row, qty_col + 1, sum(label.mapped('qty_yard')), number_format
                    )
                else:
                    worksheet.write(row, qty_col, '', number_format)
                    worksheet.write(row, qty_col + 1, '', number_format)

                qty_col += 2

            worksheet.write_number(
                row, qty_col, sum(labels_in.mapped('qty_yard')), number_format
            )

            # Labels Out
            qty_col += 1
            for grade in grades:
                label_out = labels_out.filtered(lambda l: l.grade_id == grade)
                if label_out:
                    worksheet.write_number(row, qty_col, len(label_out), number_format)
                    worksheet.write_number(
                        row,
                        qty_col + 1,
                        sum(label_out.mapped('qty_yard')),
                        number_format,
                    )
                else:
                    worksheet.write(row, qty_col, '', number_format)
                    worksheet.write(row, qty_col + 1, '', number_format)

                qty_col += 2

            worksheet.write_number(
                row, qty_col, sum(labels_out.mapped('qty_yard')), number_format
            )

            # Total Mutation
            qty_col += 1
            labels_mutation = labels_in - labels_out
            for grade in grades:
                label_mutation = labels_mutation.filtered(lambda l: l.grade_id == grade)
                if label_mutation:
                    worksheet.write_number(
                        row, qty_col, len(label_mutation), number_format
                    )
                    worksheet.write_number(
                        row,
                        qty_col + 1,
                        sum(label_mutation.mapped('qty_yard')),
                        number_format,
                    )
                else:
                    worksheet.write(row, qty_col, '', number_format)
                    worksheet.write(row, qty_col + 1, '', number_format)
                qty_col += 2

            worksheet.write_number(
                row, qty_col, sum((labels_mutation).mapped('qty_yard')), number_format
            )

            row += 1
